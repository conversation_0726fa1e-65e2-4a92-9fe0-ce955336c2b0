const fs = require("fs");
const path = require("path");
const readline = require("readline");

const skuMap = new Map();
const inputPath = path.resolve(__dirname, "output.csv"); // flattened file
const productIdPath = path.resolve(__dirname, "product_ids.csv"); // reference
const outputPath = path.resolve(__dirname, "with_product_id.csv");

// Step 1: Load product_id -> SKU mapping
function loadSkuMap() {
  return new Promise((resolve, reject) => {
    const rl = readline.createInterface({
      input: fs.createReadStream(productIdPath),
      crlfDelay: Infinity,
    });

    rl.on("line", (line) => {
      const [product_id, sku] = line.split(",");
      if (sku && product_id && sku !== "sku") {
        skuMap.set(sku.trim(), product_id.trim());
      }
    });

    rl.on("close", resolve);
    rl.on("error", reject);
  });
}

// Step 2: Read flattened CSV and add product_id column
async function process() {
  await loadSkuMap();

  const rl = readline.createInterface({
    input: fs.createReadStream(inputPath),
    crlfDelay: Infinity,
  });

  const output = fs.createWriteStream(outputPath, { encoding: "utf8" });

  // Write header (optional, assuming no header in original)
  // output.write('product_id,sku,title,...\n');

  rl.on("line", (line) => {
    const columns = line.split(",");
    const sku = columns[0].trim();
    const product_id = skuMap.get(sku) || "";

    const newLine = [product_id, ...columns].join(",");
    output.write(newLine + "\n");
  });

  rl.on("close", () => {
    output.end();
    console.log(`New CSV written to ${outputPath}`);
  });
}

process().catch((err) => console.error("Error:", err));
