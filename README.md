# Flatten CSV

Processes CSV files with multi-language product translations and flattens them into individual localized rows with product IDs.

## Overview

Transforms one row per product (with FR/ES/DE translations) into multiple rows (one per language) with localized SKUs, product IDs, and call-to-action text.

## Features

- Flattens multi-column translations into individual rows
- Maps product IDs from reference file
- Generates localized SKUs with country codes
- Adds locale-specific call-to-action text (French, Spanish, German)

## Files

**Input Files:**
- `input.csv`: Product data with columns `sku,fr,es,de`
- `product_ids.csv`: Product ID mapping with columns `id,code`

**Output:**
- `with_product_id.csv`: Flattened data with columns `product_id,name,title,checkout_title,call_to_action,locale_code`

## Usage

**Prerequisites:** Node.js 12+

**Setup:**
1. Prepare `input.csv` and `product_ids.csv` files
2. Run: `node flatten.js`
3. Output generated in `with_product_id.csv`

## Example

**Input (`input.csv`):**
```csv
61,<PERSON><PERSON> coton blanc,Mantel algodón blanco,Tischtuch Baumwolle weiss
```

**Output (`with_product_id.csv`):**
```csv
product_id,name,title,checkout_title,call_to_action,locale_code
4983,61 FR,Nappe coton blanc,Nappe coton blanc,Fiche produit,fr
4983,61 ES,Mantel algodón blanco,Mantel algodón blanco,Ficha de producto,es
4983,61 DE,Tischtuch Baumwolle weiss,Tischtuch Baumwolle weiss,Produktblatt,de
```

## How It Works

1. Loads product ID mappings from `product_ids.csv`
2. For each input row, creates 3 output rows (FR, ES, DE)
3. Generates localized SKUs: `61` → `61 FR`, `61 ES`, `61 DE`
4. Adds call-to-action text: `fr` → "Fiche produit", `es` → "Ficha de producto", `de` → "Produktblatt"

## Troubleshooting

**Empty product_id column**: SKUs in `input.csv` must match codes in `product_ids.csv`
**Missing rows**: Empty translation cells are skipped (expected behavior)
**File errors**: Ensure both input files exist in the project directory
