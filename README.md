# Flatten CSV - Multi-Language Product Data Processor

A Node.js tool that processes CSV files containing product translations in multiple languages (French, Spanish, German) and transforms them into individual localized rows with product IDs and locale-specific data.

## 🚀 Project Overview

This tool takes a CSV file with multi-column product translations and flattens it into individual rows, where each row represents a single product in a specific language. It automatically adds product IDs from a reference file, generates localized SKUs with country codes, and includes locale-specific call-to-action text.

**Input**: One row per product with translations in multiple columns  
**Output**: Multiple rows per product, one for each language with complete localized data

## ✨ Features

- **CSV Flattening**: Converts multi-column translations (FR, ES, DE) into individual rows
- **Product ID Mapping**: Automatically adds product IDs from a reference file
- **Localized SKU Generation**: Creates country-specific SKUs (e.g., "12345 FR", "12345 ES")
- **Automatic Call-to-Action**: Generates locale-specific call-to-action text
- **Multi-Language Support**: Supports French (fr), Spanish (es), and German (de) locales
- **Duplicate Content Handling**: Creates both `title` and `checkout_title` columns with identical content
- **Error Handling**: Graceful handling of missing product ID mappings

## 📁 File Structure

### Required Input Files

#### `input.csv`
Source file containing product data with translations:
```csv
sku,fr,es,de,[additional_columns...]
61,Nappe coton blanc 180 x 300 cm,Mantel algodón blanco 180 x 300 cm.,Tischtuch Baumwolle weiss 180 x 300 cm,[...]
62,Nappe coton blanc 180 x 350 cm,Mantel algodón blanco 180 x 350 cm.,Tischtuch Baumwolle weiss 180 x 350 cm,[...]
```

**Columns**:
- `sku`: Base product SKU/code
- `fr`: French translation
- `es`: Spanish translation  
- `de`: German translation
- Additional columns are ignored

#### `product_ids.csv`
Reference file mapping product IDs to SKUs:
```csv
id,code
4983,61
4984,62
4986,63
```

**Columns**:
- `id`: Product ID
- `code`: Base SKU (matches the `sku` column in input.csv)

### Output File

#### `with_product_id.csv`
Generated file with flattened, localized data:
```csv
product_id,name,title,checkout_title,call_to_action,locale_code
4983,61 FR,Nappe coton blanc 180 x 300 cm,Nappe coton blanc 180 x 300 cm,Fiche produit,fr
4983,61 ES,Mantel algodón blanco 180 x 300 cm.,Mantel algodón blanco 180 x 300 cm.,Ficha de producto,es
4983,61 DE,Tischtuch Baumwolle weiss 180 x 300 cm,Tischtuch Baumwolle weiss 180 x 300 cm,Produktblatt,de
```

**Output Columns**:
- `product_id`: Product ID from the reference file
- `name`: Localized SKU with country code (e.g., "61 FR")
- `title`: Translation text for the specific language
- `checkout_title`: Duplicate of title (for specific use cases)
- `call_to_action`: Locale-specific call-to-action text
- `locale_code`: Two-letter language code (fr, es, de)

## 🛠️ Installation & Usage

### Prerequisites
- **Node.js**: Version 12.0 or higher
- **npm**: Comes with Node.js

### Setup
1. **Clone or download** the project files
2. **Prepare your input files**:
   - Place your source data in `input.csv`
   - Place your product ID mapping in `product_ids.csv`
3. **Ensure file format** matches the expected structure (see File Structure section)

### Running the Script
```bash
node flatten.js
```

### Expected Output
- The script will create `with_product_id.csv` in the same directory
- Console output will show progress and completion status:
  ```
  Loading SKU to product_id mapping...
  Loaded 120 SKU mappings
  Processed CSV with product IDs written to [path]/with_product_id.csv
  ```

## 📊 Examples

### Input Example
**input.csv**:
```csv
61,Nappe coton blanc 180 x 300 cm,Mantel algodón blanco 180 x 300 cm.,Tischtuch Baumwolle weiss 180 x 300 cm
62,Nappe coton blanc 180 x 350 cm,Mantel algodón blanco 180 x 350 cm.,Tischtuch Baumwolle weiss 180 x 350 cm
```

**product_ids.csv**:
```csv
id,code
4983,61
4984,62
```

### Output Example
**with_product_id.csv**:
```csv
product_id,name,title,checkout_title,call_to_action,locale_code
4983,61 FR,Nappe coton blanc 180 x 300 cm,Nappe coton blanc 180 x 300 cm,Fiche produit,fr
4983,61 ES,Mantel algodón blanco 180 x 300 cm.,Mantel algodón blanco 180 x 300 cm.,Ficha de producto,es
4983,61 DE,Tischtuch Baumwolle weiss 180 x 300 cm,Tischtuch Baumwolle weiss 180 x 300 cm,Produktblatt,de
4984,62 FR,Nappe coton blanc 180 x 350 cm,Nappe coton blanc 180 x 350 cm,Fiche produit,fr
4984,62 ES,Mantel algodón blanco 180 x 350 cm.,Mantel algodón blanco 180 x 350 cm.,Ficha de producto,es
4984,62 DE,Tischtuch Baumwolle weiss 180 x 350 cm,Tischtuch Baumwolle weiss 180 x 350 cm,Produktblatt,de
```

## 🔧 Technical Details

### Data Processing Logic

1. **SKU Mapping Load**: Reads `product_ids.csv` and creates an in-memory map of `base_sku → product_id`

2. **Row Processing**: For each row in `input.csv`:
   - Extracts base SKU and translations (fr, es, de)
   - For each non-empty translation:
     - Creates localized SKU by appending country code (FR, ES, DE)
     - Looks up product_id using the base SKU
     - Generates locale-specific call-to-action text
     - Creates output row with all required columns

3. **Localized SKU Generation**: 
   - Base SKU "61" becomes "61 FR", "61 ES", "61 DE"
   - Format: `{base_sku} {country_code}`

4. **Call-to-Action Mapping**:
   - French (fr): "Fiche produit"
   - Spanish (es): "Ficha de producto"
   - German (de): "Produktblatt"

### Code Architecture

The script is organized into modular functions:
- `loadSkuMap()`: Loads product ID mappings
- `createLocalizedSku()`: Generates localized SKUs
- `processTranslationRow()`: Processes individual input rows
- `writeRow()`: Writes formatted CSV rows
- `processCSV()`: Main orchestration function

## 🐛 Troubleshooting

### Common Issues

**Empty product_id column**:
- **Cause**: SKUs in `input.csv` don't match codes in `product_ids.csv`
- **Solution**: Verify that the `sku` values in input.csv exactly match the `code` values in product_ids.csv

**Missing translations**:
- **Cause**: Empty cells in translation columns are skipped
- **Solution**: This is expected behavior - only non-empty translations generate output rows

**File not found errors**:
- **Cause**: Missing input files
- **Solution**: Ensure both `input.csv` and `product_ids.csv` exist in the project directory

**Incorrect output format**:
- **Cause**: Input CSV format doesn't match expected structure
- **Solution**: Verify input files match the documented column structure

### File Format Requirements

- **CSV Delimiter**: Comma (`,`)
- **Text Encoding**: UTF-8 recommended
- **Headers**: Not required in input files, but column order matters
- **Empty Values**: Empty translation cells are skipped (no output row generated)

## 📝 Notes

- The script processes all rows in `input.csv` regardless of content in additional columns
- Only the first 4 columns (sku, fr, es, de) are used for processing
- Product IDs that don't have a mapping will result in empty `product_id` values in the output
- The script overwrites `with_product_id.csv` on each run

## 🤝 Contributing

To modify or extend the functionality:
1. The main processing logic is in `flatten.js`
2. Modify the `callToActionMap` object to add new languages
3. Update the `countryCodes` and `localeCodes` arrays for new locales
4. Test with sample data before processing production files
