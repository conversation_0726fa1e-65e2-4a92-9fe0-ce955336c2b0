const fs = require('fs');
const readline = require('readline');
const path = require('path');

const inputPath = path.resolve(__dirname, 'input.csv');
const outputPath = path.resolve(__dirname, 'output.csv');

const rl = readline.createInterface({
  input: fs.createReadStream(inputPath),
  crlfDelay: Infinity
});

const output = fs.createWriteStream(outputPath, { encoding: 'utf8' });
output.write('name,title,locale_code\n'); // Add this line

// Constants
const countryCodes = ['FR', 'ES', 'DE'];
const localeCodes = ['fr', 'es', 'de']; // Now strictly 2-char lowercase

rl.on('line', (line) => {
  const columns = line.split(',');

  const [sku, fr, es, de, ...rest] = columns;
  const translations = [fr, es, de];

  translations.forEach((translation, i) => {
    const countryCode = countryCodes[i];
    const localeCode = localeCodes[i];

    if (translation.trim()) {
      const localizedSku = `${sku.trim()} ${countryCode}`;
      const newRow = [localizedSku, translation, localeCode].join(',');
      output.write(newRow + '\n');
    }
  });
});

rl.on('close', () => {
  output.end();
  console.log(`Flattened CSV with locale_code written to ${outputPath}`);
});
