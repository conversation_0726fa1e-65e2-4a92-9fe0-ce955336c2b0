const fs = require('fs');
const readline = require('readline');
const path = require('path');

// File paths
const inputPath = path.resolve(__dirname, 'input.csv');
const productIdPath = path.resolve(__dirname, 'product_ids.csv');
const outputPath = path.resolve(__dirname, 'with_product_id.csv');

// Constants
const countryCodes = ['FR', 'ES', 'DE'];
const localeCodes = ['fr', 'es', 'de']; // Now strictly 2-char lowercase

// Call to action translations
const callToActionMap = {
  'fr': 'Fiche produit',
  'es': 'Ficha de producto',
  'de': 'Produktblatt'
};

/**
 * Loads the SKU to product_id mapping from the reference file
 * @returns {Promise<Map>} Map of SKU to product_id
 */
function loadSkuMap() {
  return new Promise((resolve, reject) => {
    const skuMap = new Map();
    const rl = readline.createInterface({
      input: fs.createReadStream(productIdPath),
      crlfDelay: Infinity,
    });

    rl.on('line', (line) => {
      const [product_id, sku] = line.split(',');
      if (sku && product_id && sku !== 'code') {
        skuMap.set(sku.trim(), product_id.trim());
      }
    });

    rl.on('close', () => resolve(skuMap));
    rl.on('error', reject);
  });
}

/**
 * Creates a localized SKU by appending country code
 * @param {string} baseSku - The base SKU
 * @param {string} countryCode - The country code to append
 * @returns {string} The localized SKU
 */
function createLocalizedSku(baseSku, countryCode) {
  return `${baseSku.trim()} ${countryCode}`;
}

/**
 * Processes a single row of translation data into multiple localized rows
 * @param {string} line - The CSV line to process
 * @param {Map} skuMap - Map of SKU to product_id
 * @returns {Array} Array of processed row objects
 */
function processTranslationRow(line, skuMap) {
  const columns = line.split(',');
  const [sku, fr, es, de] = columns;
  const translations = [fr, es, de];
  const processedRows = [];

  translations.forEach((translation, i) => {
    const countryCode = countryCodes[i];
    const localeCode = localeCodes[i];

    if (translation.trim()) {
      const localizedSku = createLocalizedSku(sku, countryCode);
      const product_id = skuMap.get(sku.trim()) || '';
      const callToAction = callToActionMap[localeCode] || '';

      processedRows.push({
        product_id,
        name: localizedSku,
        title: translation,
        checkout_title: translation,
        call_to_action: callToAction,
        locale_code: localeCode
      });
    }
  });

  return processedRows;
}

/**
 * Writes a row object to the output stream
 * @param {Object} row - The row object to write
 * @param {fs.WriteStream} output - The output stream
 */
function writeRow(row, output) {
  const csvRow = [row.product_id, row.name, row.title, row.checkout_title, row.call_to_action, row.locale_code].join(',');
  output.write(csvRow + '\n');
}

/**
 * Main processing function that combines flattening and product ID addition
 */
async function processCSV() {
  try {
    // Load the SKU to product_id mapping
    console.log('Loading SKU to product_id mapping...');
    const skuMap = await loadSkuMap();
    console.log(`Loaded ${skuMap.size} SKU mappings`);

    // Set up input and output streams
    const rl = readline.createInterface({
      input: fs.createReadStream(inputPath),
      crlfDelay: Infinity
    });

    const output = fs.createWriteStream(outputPath, { encoding: 'utf8' });

    // Write header
    output.write('product_id,name,title,checkout_title,call_to_action,locale_code\n');

    // Process each line
    rl.on('line', (line) => {
      const processedRows = processTranslationRow(line, skuMap);
      processedRows.forEach(row => writeRow(row, output));
    });

    rl.on('close', () => {
      output.end();
      console.log(`Processed CSV with product IDs written to ${outputPath}`);
    });

    rl.on('error', (error) => {
      console.error('Error reading input file:', error);
      output.end();
    });

  } catch (error) {
    console.error('Error processing CSV:', error);
  }
}

// Execute the main processing function
processCSV();
